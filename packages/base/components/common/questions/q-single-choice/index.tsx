import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import { NButton, NPopconfirm } from 'naive-ui'
import SvgIcon from '@sa/components/custom/svg-icon.vue'
import { useQuestionsForm } from '@sa/hooks'
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import styles from './index.module.css'

export default defineComponent({
  name: 'SingleChoice',
  props: {
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },

  },
  emits: ['update:modelValue', 'update:item'],
  setup(props, { emit }) {
    const modelValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })
    const isdisabled = computed(() => props.type === 'preview')
    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })
    // 重新排序选项标识符
    const reorderOptions = (options: Array<{ label: string, value: string }>) => {
      const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return options.map((option, index) => ({
        ...option,
        value: alphabet[index] || String(index + 1),
      }))
    }
    // 响应式的选项列表
    const currentOptions = computed(() => props.item.options || [])

    // 强制更新的 key
    const forceUpdateKey = ref(0)

    // 修改 item 属性的方法
    const updateItemProperty = (property: keyof TransformToVoQuestionData, value: any) => {
      const updatedItem = { ...props.item, [property]: value }
      emit('update:item', updatedItem)
    }

    const handleChange = (value: any) => {
      if (mergedDisabled.value)
        return
      modelValue.value = value
    }

    // 删除选项 - 使用索引而不是value来避免重新排序的问题
    const handleDeleteOption = (optionIndex: number) => {
      if (!props.item.options || props.item.options.length <= 2) {
        // 至少保留2个选项
        return
      }

      if (optionIndex < 0 || optionIndex >= props.item.options.length) {
        return
      }

      // 获取要删除的选项
      const optionToDelete = props.item.options[optionIndex]

      // 记录原来的正确答案选项内容
      const originalCorrectOption = props.item.options.find(option => option.value === props.item.correctAnswer)

      // 过滤掉要删除的选项
      const filteredOptions = props.item.options.filter((_, index) => index !== optionIndex)

      // 重新排序选项标识符
      // const reorderedOptions = reorderOptions(filteredOptions)
      updateItemProperty('options', reorderedOptions)

      // 强制更新组件
      forceUpdateKey.value++

      // 处理正确答案的更新
      if (props.item.correctAnswer === optionToDelete.value) {
        // 如果删除的是正确答案，清空正确答案
        updateItemProperty('correctAnswer', '')
      }
      else if (originalCorrectOption) {
        // 如果删除的不是正确答案，根据内容找到正确答案在新排序中的位置
        const newIndex = reorderedOptions.findIndex(option => option.label === originalCorrectOption.label)
        if (newIndex !== -1) {
          updateItemProperty('correctAnswer', reorderedOptions[newIndex].value)
        }
      }
    }

    const renderOptionLabel = (option: any) => {
      if (!isdisabled.value) {
        return (
          <CKEditor
            v-model:editorValue={option.label}
            minHeight={32}
          />
        )
      }
      else {
        return (
          <span
            class={[styles.choiceItemLabel, 'contents']}
            v-html={option.label}
            v-katex
          />
        )
      }
    }

    return () => {
      return (
        <ul key={forceUpdateKey.value} class={styles.singleChoice}>
          {currentOptions.value.map((option, index) => (
            <li key={`${option.label}-${option.value}-${index}`}>
              <div class={styles.choiceItemWrapper}>
                <label class={styles.choiceItem}>
                  <div
                    class={[
                      styles.choiceItemQn,
                      !isdisabled.value && styles.cursor,
                      !isdisabled.value && props.item.correctAnswer === option.value ? styles.choiceItemQnChecked : '',
                    ]}
                    onClick={() => {
                      if (mergedDisabled.value) {
                        return
                      }
                      handleChange(option.value)
                    }}
                  >
                    <span>{option.value}</span>
                  </div>
                  {renderOptionLabel(option)}
                </label>
                {/* 编辑模式下显示删除按钮 */}
                {props.type === 'edit' && props.item.options && props.item.options.length > 2 && (
                  <NPopconfirm
                    onPositiveClick={() => handleDeleteOption(index)}
                    positiveText="确认删除"
                    negativeText="取消"
                  >
                    {{
                      trigger: () => (
                        <NButton
                          size="small"
                          quaternary={true}
                          class={styles.deleteButton}
                        >
                          <SvgIcon icon="mdi:close" />
                        </NButton>
                      ),
                      default: () => `确定要删除选项 ${option.value} 吗？`,
                    }}
                  </NPopconfirm>
                )}
              </div>
            </li>
          ))}
        </ul>
      )
    }
  },
})
